"""
Main FastAPI application entry point.
"""

from fastapi import FastAPI
from fastapi.responses import RedirectResponse
from fastapi.middleware.cors import CORSMiddleware
import nltk

from src.helper.logger import setup_new_logging
from src.core.activity_log import UserActivityMiddleware
from src.core.exceptions import add_exception_handlers
from main_routes import include_routers, nltk_download
from src.v3 import v3_app

# Initialize logger
loggers = setup_new_logging(__name__)
loggers.info("Application starting")

# Import and create a lifespan context manager for the application
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Lifespan context manager for the FastAPI application.
    This is the recommended way to handle startup and shutdown events.
    """
    # Startup: Download NLTK data asynchronously
    loggers.info("Starting NLTK data download in application startup")
    await nltk_download()
    loggers.info("NLTK data download completed")

    # Yield control back to FastAPI
    yield

    # Shutdown: Clean up resources if needed
    loggers.info("Application shutting down")

# Create FastAPI application with lifespan context manager
app = FastAPI(
    title="Eko Backend",
    description="Eko AI API",
    version="0.1.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "*",  # Allow all origins for development
           "http://localhost:3000",
           "http://localhost:8201",
           "http://127.0.0.1:3000",
           "http://127.0.0.1:8201",
           "https://eko-api2.nextai.asia",
           "https://eko.nextai.asia",
           "https://eko-dev.nextai.asia",
           "https://dramit-dev.thebeauty.ai",
           "https://dramit.thebeauty.ai",
           "https://sociair.io",
           "https://*.sociair.io",
           "http://sociair.io",
           "http://*.sociair.io",
          "https://ag.sociair.io"
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Note: Referrer checking for login requests is handled via a custom dependency
# in src/models/security.py (get_login_form_with_referrer_check) which is used
# in the login endpoint to set client_id based on a flexible referrer configuration.
# The configuration maps client_ids to lists of referrer domains, allowing multiple
# domains to be associated with each client_id.


# Add user activity tracking middleware
app.add_middleware(
    UserActivityMiddleware,
    exclude_paths=["/docs", "/openapi.json", "/redoc"],  # Exclude documentation paths
    auto_error=False  # Don't raise errors for missing tokens
)

# Include all routers from the main_routes module
include_routers(app)

# Mount v3 API as a sub-application
app.mount("/v3", v3_app)

# Add exception handlers from the core.exceptions module
add_exception_handlers(app)
# app.mount("/v3/chat", v3_router)
# Router inclusion and exception handling complete

# Root endpoint redirects to API documentation
@app.get("/")
async def root():
    return RedirectResponse(url="/docs")

# Health check endpoint for load balancer
@app.get("/health")
async def health_check():
    """Health check endpoint for load balancer and monitoring."""
    return {
        "status": "healthy",
        "service": "eko-backend",
        "version": "0.1.0"
    }

# Automatically apply logging to all routes

loggers.info("Application started successfully")