from datetime import datetime
from dotenv import load_dotenv
import os
from pymongo import AsyncMongoClient
from pydantic import BaseModel
from typing import Optional, Literal

from src.core.security import (
    create_access_token,
     get_tenant_info,
    pwd_context
)

from src.v2.dashboard.users import get_extended_token,get_tenant_id_and_name_from_slug
from src.models.security import ExtendedTokenRequest

load_dotenv()
class UserRegistration(BaseModel):
    username: str
    password: str
    role:Optional[Literal["admin", "supervisor", "agent"]] = "admin"




class Tenant:
    def __init__(self,name,slug,requirements):
        self.name=name
        self.slug=slug
        self.mongo_url=os.getenv("MONGO_URI")
        self.admin_db=os.getenv("DATABASE_NAME")
        self.requirements=requirements

    def _get_required_list(self,type):
        return self.requirements[type]
    
    def _admin_db_connection(self):
        """to get admin database connection"""
        return AsyncMongoClient(self.mongo_url)[self.admin_db]


    def _prepare_tenant(self):
        """prepare tenant data"""
        name=self.name
        slug=self.slug.replace(" ", "_")
        database_name=f"eko_{slug}_db"
        label=self.name.title()
        
        return {
            "name":name,
            "slug":slug,
            "database_name":database_name,
            "label":label,
            "topic_generation":False
        }
    
    async def _register_tenant(self):
        """ to register tenant in admin database"""
        tenant_data = self._prepare_tenant()
        client = self._admin_db_connection()
        collections = await client.list_collection_names()
        if "tenants" in collections:
            collection = client["tenants"]
        else:
            collection = await client.create_collection("tenants")
        result = await collection.insert_one(tenant_data)
        # print id of tenant
        print(result.inserted_id)
        if result.acknowledged:
            print("Tenant registered successfully")
        else:
            raise Exception("Tenant registration failed")

        return True
    
    async def _get_tenant_id(self):
        """to get tenant id from slug"""
        client = self._admin_db_connection()
        print(f"Looking for tenant with slug: {self.slug}")
        result = await client.tenants.find_one({"slug": self.slug})
        if result:
            print(f"Found tenant with ID: {result['_id']}")
            return result["_id"]
        else:
            # List all available tenants for debugging
            all_tenants = await client.tenants.find().to_list(length=10)
            print(f"Available tenants: {[t.get('slug') for t in all_tenants]}")
            raise Exception(f"Tenant with slug '{self.slug}' not found")

    async def _preapre_client_database(self):
        """
        for creating database and collections for client
        """
        tenant_data = self._prepare_tenant()
        database_name = tenant_data["database_name"]
        client = AsyncMongoClient(self.mongo_url)
        database_names = await client.list_database_names()
        if database_name in database_names:
            raise Exception(f"{database_name} Database already exists")
    
        client_db = client[database_name]
        
        collection=["users","tools","settings","prompt"]
        for col in collection:
            collection_names = await client_db.list_collection_names()
            if col in collection_names:
                continue
            else:
                await client_db.create_collection(col)
                print(f"{col} collection created successfully")

        print("Database created successfully")
        return True
        
    async def _get_client_db(self):
        """to get client database"""
        tenant_data = self._prepare_tenant()
        database_name = tenant_data["database_name"]
        client = AsyncMongoClient(self.mongo_url)
        database_names = await client.list_database_names()
        if database_name in database_names:
            return client[database_name]
        else:
            raise Exception(f"{database_name} Database does not exist")
    
    async def _prepare_env(self):
        env_schema = await self._admin_db_connection()["settings"].find_one({"name": "env"})
        env_schema["qdrant_config"]["coll_name"] = f"{self.slug}_sentence_context"
        env_schema["qdrant_config"]["sentence_collection"] = f"{self.slug}_sentence_context"
        env_schema["qdrant_config"]["page_collection"] = f"{self.slug}_test_page_info"
        env_schema["qdrant_config"]["sentence_split_collection"] = f"{self.slug}_sentence_split"
        env_schema["minio_config"]["bucket_name"] = f"eko.{self.slug.lower().replace(' ', '-').replace('_', '-')}"

        return env_schema
    
    async def _insert_default_data(self):
        """
        for inserting default data in client database
        """
        admin_db = self._admin_db_connection()
        print("admin_db",admin_db)
        # collections = await admin_db.list_collection_names()
        default_user = await admin_db["users"].find_one({"username": "superadmin"})
        print("default_user",default_user)
        if not default_user:
            raise Exception("Default superadmin user not found in admin database")

        client_db = await self._get_client_db()
        result = await client_db.users.insert_one(default_user)
        if not result.acknowledged:
            raise Exception("Default user insertion failed")
  
 
        cursor = admin_db["prompt"].find({"name": {"$in": self._get_required_list("required_prompt")}})
        default_prompts = await cursor.to_list()

        result = await client_db.prompt.insert_many(default_prompts)
        if not result.acknowledged:
            raise Exception("Default prompt insertion failed")
   

        cursor = admin_db["tools"].find({"name": {"$in": self._get_required_list("required_tools")}})
        default_tools = await cursor.to_list()

        result = await client_db.tools.insert_many(default_tools)
        if not result.acknowledged:
            raise Exception("Default tools insertion failed")
 

        cursor = admin_db["settings"].find({"name": {"$in": self._get_required_list("required_settings")}})
        default_settings = await cursor.to_list(length=100)

        result = await client_db.settings.insert_many(default_settings)
        if not result.acknowledged:
            raise Exception("Default settings insertion failed")

        env_schema = await self._prepare_env()
 
        result = await client_db.settings.insert_one(env_schema)
        if not result.acknowledged:
            raise Exception("env settings insertion failed")
        
        test_user=await admin_db["requirements"].find_one({"username": "testuser"})
        if not test_user:
            raise Exception("Default test user not found in admin database")

        # result = await client_db.users.insert_one(test_user)
        # pass to register
        registration_data = {
            "username": test_user["username"],
            "password": test_user["plainpassword"],
            "role": test_user.get("role", "admin"),
            "permissions": test_user.get("permissions", None)
        }

        user_data=UserRegistration(**registration_data)

        result=await self.register_user(user_data)
        if not result.acknowledged:
            raise Exception("Default test user insertion failed")

        
        print("Default data insertion completed")
        return True
    

    async def register_user(self,registration: UserRegistration):
        """
        Register a new agent directly not via invitation token.
        """

        client_db = await self._get_client_db()
        existing_user = await client_db.users.find_one({"username": registration.username})
        if existing_user:
            return {"msg": "Username already exists!", "success": False}

        hashed_password = pwd_context.hash(registration.password)

        # Create the new agent user
        new_agent = {
            "username": registration.username,
            "hashed_password": hashed_password,
            "role": registration.role,
            "created_by": "superadmin",
            "created_at": datetime.now(),
        }

        # Insert the new agent into the database
        result = await client_db.users.insert_one(new_agent)
        tenant_id = await self._get_tenant_id()
        request = ExtendedTokenRequest(
            username=registration.username,
            password=registration.password,
            client_id=self.slug,
            days=365
        )
        extended_token = await get_extended_token(request)
        token = extended_token["access_token"]
        await client_db.users.update_one({"_id": result.inserted_id}, {"$set": {"hashed_password": token}})



        if not result.acknowledged:
            raise Exception("Test Agent registration failed")
            
        new_agent["_id"] = result.inserted_id

        return result
