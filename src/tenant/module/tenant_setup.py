from datetime import datetime
from typing import Optional
from pydantic import BaseModel
from fastapi import HTTPException
from src.core.database import get_admin_db
class ProductDetail(BaseModel):
    name:str
    detail:str

class BusinessInfo(BaseModel):
    org_name:str
    org_type:str
    org_description:Optional[str]=None
    org_goal:Optional[str]=None
    org_contact:Optional[str]=None
    org_email:Optional[str]=None
    agent_name:Optional[str]="AI Bot"
    # agent_role:Optional[str]=None
    language:Optional[list[str]]=["English"]
    additional_agent_goal:Optional[str]=None
    agent_goal_type:Optional[str]=None

    set_up_complete:Optional[bool]=False

    def format_dummy_prompt(self, current_user, response_mode_str: str="Provide a detailed and explained answer in a couple of sentences."):
        simplified_prompt = get_admin_db().prompt.find_one({"name": "reply_prompt_openai_simplified_setup"})

        if simplified_prompt:
            simplified_prompt.pop("_id", None)
            simplified_prompt["name"] = "reply_prompt_openai_simplified"

            # Upsert into user's DB
            current_user.db.prompt.update_one(
                {"name": "reply_prompt_openai_simplified"},
                {"$set": simplified_prompt},
                upsert=True
            )
        else:
            raise Exception("Admin prompt 'reply_prompt_openai_simplified_setup' not found")


        standard_prompt = get_admin_db().prompt.find_one({"name": "reply_prompt_openai_setup"})

        if standard_prompt:
            standard_prompt.pop("_id", None)
            standard_prompt["name"] = "reply_prompt_openai"

            # Upsert into user's DB
            current_user.db.prompt.update_one(
                {"name": "reply_prompt_openai"},
                {"$set": standard_prompt},
                upsert=True
            )
        else:
            raise Exception("Admin prompt 'reply_prompt_openai_setup' not found")


        elaborated_prompt = get_admin_db().prompt.find_one({"name": "reply_prompt_openai_elaborated_setup"})
        if elaborated_prompt:
            elaborated_prompt.pop("_id", None)
            elaborated_prompt["name"] = "reply_prompt_openai_elaborated"

            # Upsert into user's DB
            current_user.db.prompt.update_one(
                {"name": "reply_prompt_openai_elaborated"},
                {"$set": elaborated_prompt},
                upsert=True
            )
        else:
            raise Exception("Admin prompt 'reply_prompt_openai_elaborated_setup' not found")
        initial_address_prompt=get_admin_db().prompt.find_one({"name": "initial_address_information_setup"})
        if initial_address_prompt:
            initial_address_prompt.pop("_id", None)
            initial_address_prompt["name"] = "initial_address_information"

            # Upsert into user's DB
            current_user.db.prompt.update_one(
                {"name": "initial_address_information"},
                {"$set": initial_address_prompt},
                upsert=True
            )
        else:
            raise Exception("Admin prompt 'initial_address_information_setup' not found")

        return True
# 
# 
    # @classmethod
    # def update_prompt(cls, prompt: str, tools: list[str], ticket_required_info: list[str] | None,AI_greeting:bool, current_user):
    #     updated_tools = []

    #     if tools:
    #         for tool_name in tools:
    #             tool_definition = current_user.db.tools.find_one({"name": "create_issue_tickets"})
    #             if tool_definition:
    #                 enum_path = tool_definition["tools"][0]["function"]["parameters"]["properties"]["issue_type"]["enum"]
    #                 required_path = tool_definition["tools"][0]["function"]["parameters"].get("required", [])

    #                 # Update enum if not already present
    #                 if tool_name not in enum_path:
    #                     enum_path.append(tool_name)

    #                 update_fields = {
    #                     "updated_at": datetime.now(),
    #                     "tools.$[tool].function.parameters.properties.issue_type.enum": enum_path
    #                 }

    #                 # ✅ Option A: MERGE MODE (add new required fields to existing ones)
    #                 # if ticket_required_info:
    #                 #     merged_required = list(set(required_path + ticket_required_info))
    #                 #     update_fields["tools.$[tool].function.parameters.required"] = merged_required

    #                 # 🔁 Option B: REPLACE MODE (overwrite existing required fields)
    #                 if ticket_required_info is not None:
    #                     update_fields["tools.$[tool].function.parameters.required"] = ticket_required_info

    #                 current_user.db.tools.update_one(
    #                     {"name": "create_issue_tickets"},
    #                     {"$set": update_fields},
    #                     array_filters=[{
    #                         "tool.type": "function",
    #                         "tool.function.name": "create_issue_tickets"
    #                     }]
    #                 )

    #                 updated_tools.append(tool_name)

    #     # Update business_info
    #     current_user.db.business_info.update_one(
    #         {"name": "BusinessInfo"},
    #         {
    #             "$set": {
    #                 "preferred_prompt": prompt,
    #                 "set_up_complete": True,
    #                 "used_tools": updated_tools,
    #                 "ticket_required_info": ticket_required_info if ticket_required_info else [],
    #                 "AI_greeting":AI_greeting,
    #                 "updated_at": datetime.now()
    #             }
    #         },
    #         upsert=True
    #     )

    #     return {
    #         "status": "success tool and preferred prompt added"
    #     }



    @classmethod
    def _update_ticket_tools(
        cls, 
        tools: list[str], 
        ticket_required_info: list[str] | None, 
        current_user
    ) -> list[str]:
        """Handle ticket tools updates, including required fields and their properties"""
        updated_tools = []

        if not tools:
            return updated_tools

        for tool_name in tools:
            if tool_name != "create_issue_tickets":
                continue

            tool_doc = current_user.db.tools.find_one({"name": "create_issue_tickets"})
            if not tool_doc:
                continue

            tool = next((t for t in tool_doc["tools"] if t["function"]["name"] == "create_issue_tickets"), None)
            if not tool:
                continue

            function_def = tool["function"]
            parameters = function_def.get("parameters", {})
            properties = parameters.get("properties", {})
            existing_required = set(parameters.get("required", []))

            enum_path = properties.get("issue_type", {}).get("enum", [])
            if tool_name not in enum_path:
                enum_path.append(tool_name)

            # 🛠 Ensure all required fields exist in properties
            updated_properties = dict(properties)  # deep copy if necessary

            if ticket_required_info is not None:
                ticket_required_info = list(set(ticket_required_info))  # remove duplicates

                for field in ticket_required_info:
                    if field not in updated_properties:
                        updated_properties[field] = {
                            "type": "string",
                            "description": f"Required field: {field.replace('_', ' ').capitalize()}."
                        }

                # Remove fields from properties that are no longer required
                for field in list(updated_properties):
                    if field not in ticket_required_info and field not in ("name", "contact", "description", "issue_type"):
                        updated_properties.pop(field)

            # Prepare update query
            update_fields = {
                "updated_at": datetime.now(),
                "tools.$[tool].function.parameters.properties": updated_properties,
                "tools.$[tool].function.parameters.properties.issue_type.enum": enum_path,
            }

            if ticket_required_info is not None:
                update_fields["tools.$[tool].function.parameters.required"] = ticket_required_info

            current_user.db.tools.update_one(
                {"name": "create_issue_tickets"},
                {"$set": update_fields},
                array_filters=[{
                    "tool.type": "function",
                    "tool.function.name": "create_issue_tickets"
                }]
            )

            updated_tools.append(tool_name)

        return updated_tools


    @classmethod
    def _update_booking_tools(
        cls,
        booking: list[str],
        booking_required_info: list[str],
        current_user
    ) -> list[str]:
        """Handle booking tools updates: required fields + properties cleanup"""
        updated_tools = []

        if "handle_booking" not in booking:
            return updated_tools

        booking_doc = current_user.db.tools.find_one({"name": "handle_booking"})
        if not booking_doc:
            return updated_tools

        # Get the correct tool
        tool = next((t for t in booking_doc["tools"] if t["function"]["name"] == "handle_booking"), None)
        if not tool:
            return updated_tools

        parameters = tool["function"].get("parameters", {})
        properties = parameters.get("properties", {})
        existing_required = set(parameters.get("required", []))

        # 🧼 Clean and deduplicate required list
        booking_required_info = list(set(booking_required_info or []))

        # ⚙️ Ensure required fields exist in properties
        updated_properties = dict(properties)  # Shallow copy

        for field in booking_required_info:
            if field not in updated_properties:
                updated_properties[field] = {
                    "type": "string",
                    "description": f"Required field: {field.replace('_', ' ').capitalize()} to create booking."
                }

        # 🔥 Remove obsolete properties (not in required and not base fields)
        protected_fields = { "description"}
        for key in list(updated_properties.keys()):
            if key not in booking_required_info and key not in protected_fields:
                updated_properties.pop(key)

        # Prepare the MongoDB update
        booking_update_fields = {
            "updated_at": datetime.now(),
            "tools.$[tool].function.parameters.required": booking_required_info,
            "tools.$[tool].function.parameters.properties": updated_properties
        }

        result = current_user.db.tools.update_one(
            {"name": "handle_booking"},
            {"$set": booking_update_fields},
            array_filters=[{
                "tool.type": "function",
                "tool.function.name": "handle_booking"
            }]
        )

        updated_tools.append("handle_booking")
        return updated_tools

    @classmethod
    def update_prompt(
        cls, 
        prompt: str, 
        tools: list[str], 
        ticket_required_info: list[str] | None, 
        booking: list[str], 
        booking_required_info: list[str], 
        AI_greeting: bool, 
        current_user
    ):
        """Main method to update prompt and tools"""
        print("Received booking data:", booking)

        # Handle ticket tools
        ticket_tools = cls._update_ticket_tools(tools, ticket_required_info, current_user)
        
        # Handle booking tools
        booking_tools = cls._update_booking_tools(booking, booking_required_info, current_user)
        
        # Combine updated tools
        updated_tools = ticket_tools + booking_tools

        # Update business_info
        current_user.db.business_info.update_one(
            {"name": "BusinessInfo"},
            {
                "$set": {
                    "preferred_prompt": prompt,
                    "set_up_complete": True,
                    "used_tools": updated_tools,
                    "ticket_required_info": ticket_required_info if ticket_required_info else [],
                    "booking_required_info": booking_required_info if booking_required_info else [],
                    "booking_types": booking if booking else [],
                    "AI_greeting": AI_greeting,
                    "updated_at": datetime.now()
                }
            },
            upsert=True
        )

        return {
            "status": "success tool and preferred prompt added"
        }