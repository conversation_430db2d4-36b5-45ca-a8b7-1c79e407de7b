from pydantic import BaseModel, Field, field_validator
from typing import Any, Dict, Literal,Optional
# from src.reply.minio_client import MinIOClient,MinIOConfig


class User(BaseModel):
    """
    User document from the tenant database -> users collection
    """
    id: Any = Field(alias="_id")
    username: str
    role: Literal["admin", "supervisor", "agent"]

    @field_validator("id")
    def convert_objid_to_str(cls, value):
        return str(value)

class UserTenantDB(BaseModel):
    tenant_id: str
    tenant_database_name: str
    slug: str
    db: Any
    user: User
    async_db: Any
    access: Optional[Dict] = None

    # @property
    # def minio_client(self) -> MinIOClient:
    #     env_var = self.db.settings.find_one({"name": "env"})
    #     minio_conf=env_var.get("minio_config")
    #     minio = MinIOClient(config=MinIOConfig(access_key=minio_conf.get("access_key"),secret_key=minio_conf.get("secret_key"),minio_url=minio_conf.get("minio_url"),bucket_name=minio_conf.get("bucket_name")))
    #     return minio
    
    @property
    def qdrant_config(self) -> dict:
        env_var = self.db.settings.find_one({"name": "env"})
        qd_config = env_var["qdrant_config"]
        qdrant_coll = qd_config["sentence_collection"]
        return {"coll_name": qdrant_coll}
        # qd_client = Qdrant_Call(config=QdrantConfig(coll_name=qdrant_coll))
        # return qd_client

    @property
    def minio_config(self) -> dict:
        env_var = self.db.settings.find_one({"name": "env"})
        minio_conf = env_var.get("minio_config")
        return {
            "access_key": minio_conf.get("access_key"),
            "secret_key": minio_conf.get("secret_key"),
            "minio_url": minio_conf.get("minio_url"),
            "bucket_name": minio_conf.get("bucket_name")
        }

    
class AgentInvitation(BaseModel):
    username: str = Field(..., example="agent_username")
    role: Literal["admin", "supervisor", "agent"]
    permissions: Dict[str, bool]| None={"all":True}
    expires_at: int = Field(default=7, description="Expiration in days")
    
class AgentRegistration(BaseModel):
    username: str = Field(..., example="agent_username") 
    role: Literal["admin", "supervisor", "agent"]
    password: str = Field(..., example="strongpassword123")
    token: str = Field(..., example="invitation_token_here")

class UserRegistration(BaseModel):
    db_name:str=Field(..., example="Database name") 
    username: str = Field(..., example="agent_username") 
    role: Literal["admin", "supervisor", "agent"]
    password: str = Field(..., example="strongpassword123")
    isBot : Optional[bool]=False