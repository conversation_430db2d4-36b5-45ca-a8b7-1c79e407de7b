from typing import List, Dict
import asyncio
import httpx

from src.models.user import UserTenantDB
from src.reply.minio_client import MinIO<PERSON>lient, MinIOConfig
from src.v2.external_hooks.social_media_webhooks.models import MediaItem
from fastapi import APIRouter
from src.helper.logger import setup_new_logging

loggers = setup_new_logging(__name__)
router = APIRouter()

@router.post("/upload_to_sociair", response_model=List[str])
async def upload_to_sociair(media_urls: List[MediaItem], current_user: UserTenantDB) -> List[str]:
    """
    Uploads media files from MinIO to Sociair's media upload endpoint.

    :param media_urls: List of MinIO object names (files) to upload.
    :param current_user: Authenticated user with access to minio_config and async_db.
    :return: List of Sociair media IDs.
    """
    # minio_client = MinIOClient(**current_user.minio_config)
    minio_client = MinIOClient(config=MinIOConfig(**current_user.minio_config))


    access_token_doc = await current_user.async_db.settings.find_one(
        {"name": "sociar_env"},
        {"API_TOKEN": 1, "BASE_URL": 1, "_id": 0}
    )

    url = f"{access_token_doc['BASE_URL']}/master/medias/upload"
    if current_user.slug=="dramit-dev" and "dramit":
        slug="beautyai"
    else:
        slug=current_user.slug
    base_headers = {
        'Authorization': f'Bearer {access_token_doc["API_TOKEN"]}',
        'Accept': 'application/json, text/plain, */*',
        'Origin': access_token_doc.get("origin", f"https://{slug}.sociair.io"),
        'Referer': access_token_doc.get("referer", f"https://{slug}.sociair.io/"),
    }

    tasks = [
        upload_to_sociair_bytes(
            await asyncio.to_thread(minio_client.get_object_bytes,  # non-blocking
                bucket_name=current_user.minio_config["bucket_name"],
                object_name=media.name,
                folder="Images/test"
            ),
            base_headers,
            url
        )
        for media in media_urls
    ]

    responses = await asyncio.gather(*tasks)

    # Extract valid media IDs
    # sociair_ids = [resp.get("id") for resp in responses if resp and resp.get("id")]
    sociair_ids = [item['id'] 
               for resp in responses 
               if resp and 'data' in resp 
               for item in resp['data'] 
               if item and 'id' in item]
    return sociair_ids


async def upload_to_sociair_bytes(file_bytes: bytes, base_headers: Dict[str, str], url: str) -> dict:
    """
    Uploads a single file (in bytes) to Sociair's media upload endpoint.

    :param file_bytes: The bytes of the file to be uploaded.
    :param base_headers: Base headers including Authorization.
    :param url: The full URL for uploading.
    :return: Response from Sociair API.
    """
    try:
        # Use httpx's built-in multipart support instead of MultipartEncoder
        files = {
            'uploaded_file[0]': ('Screenshot.png', file_bytes, 'image/png')
        }

        data = {
            'upload_for': 'media'
        }

        # Don't set Content-Type manually - httpx will set it automatically for multipart
        headers = {
            **base_headers
            # Remove Content-Type - httpx will set it automatically with correct boundary
        }

        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=headers, files=files, data=data)

        return response.json()
    except Exception as e:
        loggers.error(f"Error uploading to Sociair: {str(e)}")
        return []