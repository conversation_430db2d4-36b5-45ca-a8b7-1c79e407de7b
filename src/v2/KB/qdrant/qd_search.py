# Standard library imports
from typing import Dict, List, Optional, Union
from typing_extensions import ClassVar, Literal
from bson import ObjectId
from itertools import repeat
import asyncio

# Third-party imports
from fastapi import APIRouter, Depends, HTTPException,status
from fastapi.responses import JSONResponse
from pydantic import BaseModel, field_validator
from llama_index.core.schema import NodeWithScore

# Local imports
from src.models.credit import  CreditManager, get_credit_info
from src.core.security import get_tenant_info
from src.models.user import UserTenantDB
from src.helper.qdrant import (
    fetch_qdrant_config,
    initialize_clients,
    process_metadata,
    process_source_urls,
    extract_page_sent,
    process_qdrant_item,
)
from src.helper import logger

loggers=logger.setup_new_logging(__name__)

# Define the router for the search endpoint
search_router = APIRouter(tags=["Search"])


class Search_query(BaseModel):
    query: str
    metadata: Optional[List[Dict]] = None
    level: Optional[Literal["level-1", "level-2", "level-3"]] = None
    prompt: Optional[str] = None

    @field_validator("metadata", mode="before")
    @classmethod
    def transform_metadata(cls, metadata):
        if not metadata:
            return []

        return [
            {"key": k, "value": v, "operator": "in" if isinstance(v, list) else "=="}
            for item in metadata
            for k, v in item.items()
        ]

    levels: ClassVar[Dict[str, int]] = {"level-1": 3, "level-2": 5, "level-3": 8}
    prompts: ClassVar[Dict[str, str]] = {
        "level-1": "Do not rely on your own knowledge. Provide a concise and to the point answer to the question\n Question : {query} mentioned in the document",
        "level-2": "Do not rely on your own knowledge. Provide a through answer to the question\n Question : {query} mentioned in the document",
        "level-3": "Do not rely on your own knowledge. Provide an in-depth and thorough answer to the question : \n Question : {query} mentioned in the document",
    }

    def __init__(self, **data):
        super().__init__(**data)
        if self.level:
            self.prompt = self.prompts[self.level].format(query=self.query)
            self.level = self.levels[self.level]
        else:
            raise HTTPException(status_code=400, detail="level is required")


class SearchAll(BaseModel):
    collection_name: Optional[str] = ("test_page_info",)
    limit: int = 15
    offset: Optional[Union[str, int]] = None
    filter: Optional[List[Dict]] = None

    @field_validator("filter")
    @classmethod
    def validate_filter(cls, v):
        from datetime import datetime

        filter_date = (
            datetime.now()
            .replace(hour=23, minute=59, second=59, microsecond=999999)
            .strftime("%Y-%m-%d %H:%M:%S")
        )
        if not v:
            return None

        if any(not item for item in v):
            return None

        return {
            "must": [
                {"key": k, "match": {"value": v}} for item in v for k, v in item.items()
            ],
            # "should": [{"key": "updated_at", "range": {"lte": filter_date}}],
        }


@search_router.post("/retrieve")
async def qdrant_search(
    request: Search_query, current_user: UserTenantDB = Depends(get_tenant_info)
):
    try:
        # Step 1: Load Qdrant configuration
        qdrant_config, minio_config = await fetch_qdrant_config(current_user)

        # Step 2: Initialize Clients
        qarant, minio = await initialize_clients(qdrant_config, minio_config)

        # Step 3: Perform Query
        response = await qarant.query_engine(
            request.prompt, request.metadata or None, request.level
        )

        # Step 4: Process Metadata
        filtered_nodes = await process_metadata(response, qarant, qdrant_config, minio)

        # Step 5: Process Source URLs
        response.metadata = await process_source_urls(response, minio)

        return {
            "response": response.response,
            "source_nodes": filtered_nodes,
            "metadata": response.metadata,
        }

    except Exception as e:
        import traceback

        traceback.print_exc()
        return {"source_nodes": []}


@search_router.post("/search")
async def qdrant_search(
    request: Search_query, current_user: UserTenantDB = Depends(get_tenant_info)
): 
    try:
        # per_cost, remaining_credit = get_credit_info(cost_type="KB",current_user=current_user)
        # if remaining_credit < per_cost:
        #     raise HTTPException(status_code=402, detail=f"Insufficient credits. Required: {per_cost}, Available: {remaining_credit}")
        if not request.level:
            raise HTTPException(status_code=500, detail="level is required")

        # Step 1: Load Qdrant & MinIO Configurations
        qdrant_config, minio_config = await fetch_qdrant_config(current_user)

        # Step 2: Initialize Clients
        qarant, minio = await initialize_clients(qdrant_config, minio_config)

        count=qarant.client_().count(collection_name=qdrant_config["page_collection"])
        credit_manager = CreditManager(current_user.db)


        if count.count==0:
            raise HTTPException(status_code=404, detail="No documents found in Database")
            return {"source_nodes": [],"response":None,"metadata":[]}

        # Step 3: Perform Query
        response = await qarant.query_engine(
            request.prompt, request.metadata or None, request.level
        )
        # credit_result = credit_manager.deduct_credits(
        #     amount=per_cost,
        #     description="Fact Checking"
        # )
        
        # if not credit_result["success"]:
        #     loggers.error(f"Failed to deduct credits while checking fact: {credit_result['message']}")


        # Step 4: Process Metadata
        filtered_nodes: List[NodeWithScore] = await extract_page_sent(
            response.source_nodes, qdrant_config, qarant.client_(), minio, current_user
        )
        filtered_nodes_list = []
        for node in filtered_nodes:
            if not node:
                continue

            updated_by = node.metadata.get("updated_by")
            print(f"updated_by: {updated_by}")
            if updated_by:
                user = current_user.db.users.find_one({"_id": ObjectId(updated_by)})
                node.metadata["updated_by"] = user.get("username") if user else updated_by

            node_ = {
                "id_": node.metadata.get("hash_id"),
                "extra_info": node.metadata,
                "text": node.text,
                "score": node.score,
                "sentence": node.metadata.get("sentence"),
                "created_at": node.metadata.get("created_at"),
                "updated_at": node.metadata.get("updated_at"),
                "updated_by": node.metadata.get("updated_by")
            }
            filtered_nodes_list.append(node_)

        # sort the filtered_nodes_list by score
        filtered_nodes_list.sort(key=lambda x: x.get("score"), reverse=True)
        # Step 5: Process Source URLs
        response.metadata = await process_source_urls(response, minio)

        return {
            "response": response.response,
            "source_nodes": filtered_nodes_list,
            "metadata": response.metadata,
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))


@search_router.post("/search_all")
async def qdrant_search(
    request: SearchAll, current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Paginated search endpoint that returns all documents from Qdrant collection.
    Supports filtering and pagination with offset.

    Args:
        request: SearchAll object containing filter, limit, and offset parameters
        current_user: Current user tenant information

    Returns:
        Dictionary with next_offset for pagination and response with processed results
    """
    try:
        # Step 1: Load Qdrant configuration
        qdrant_config, minio_config = await fetch_qdrant_config(current_user)

        # Step 2: Initialize Clients
        qarant, minio = await initialize_clients(qdrant_config, minio_config)

        # Check if collection has any documents
        count = qarant.client_().count(collection_name=qdrant_config["page_collection"])
        if count.count == 0:
            raise HTTPException(status_code=404, detail="No documents found in Database")

        # Step 3: Set up common scroll parameters
        scroll_params = {
            "collection_name": qdrant_config["page_collection"],
            "limit": request.limit,
            "with_payload": True,
        }

        # Step 4: Handle different query scenarios
        if request.filter:
            # When using filter
            scroll_params["scroll_filter"] = request.filter

            # If we also have an offset with filter, we need to add a range condition
            if request.offset:
                # Create a filter that includes the original filter conditions
                # plus a condition to only get records with updated_at < offset
                combined_filter = {
                    "must": request.filter.get("must", []) + [
                        {
                            "key": "updated_at",
                            "range": {
                                "lt": request.offset
                            }
                        }
                    ]
                }
                scroll_params["scroll_filter"] = combined_filter
            else:
               order_by = {
                "key": "updated_at",
                "direction": "desc",}
               scroll_params["order_by"] = order_by
        else:
            # When not using filter, use order_by for pagination
            order_by = {
                "key": "updated_at",
                "direction": "desc",
            }

            # Add start_from for pagination if offset is provided
            if request.offset:
                order_by["start_from"] = request.offset

            scroll_params["order_by"] = order_by

        # Step 5: Execute the scroll query
        # We don't use next_offset from scroll when using order_by
        response, _ = qarant.client_().scroll(**scroll_params)

        # Step 6: Handle empty response
        if not response:
            return {"next_offset": None, "response": [], "has_more": False}

        # Step 7: Process results in parallel and filter out duplicates
        # Extract unique points by ID to avoid duplicates
        seen_ids = set()
        unique_response = []

        for item in response:
            item_id = item.id
            if item_id not in seen_ids:
                seen_ids.add(item_id)
                unique_response.append(item)

        tasks = [process_qdrant_item for _ in range(len(unique_response))]
        processed_results = await asyncio.gather(
            *(task(i, minio, current_user) for i, task in zip(unique_response, tasks))
        )

        # Step 8: Determine if there are more results and the next offset
        # We consider there are more results if we got a full page of results
        has_more = len(response) == request.limit

        # Get the last point's updated_at value for next pagination
        last_point = None
        if unique_response:
            # Use the last unique item for pagination
            last_point = unique_response[-1].payload.get("updated_at")

            # If we filtered out duplicates, we need to check if there are more results
            if len(unique_response) < request.limit and has_more:
                # Do an additional query to check if there are more results
                additional_params = dict(scroll_params)

                if request.filter:
                    # Create a filter that excludes the results we've already seen
                    combined_filter = {
                        "must": request.filter.get("must", []) + [
                            {
                                "key": "updated_at",
                                "range": {
                                    "lt": last_point
                                }
                            }
                        ]
                    }
                    additional_params["scroll_filter"] = combined_filter
                else:
                    # When using order_by
                    additional_params["order_by"] = {
                        "key": "updated_at",
                        "direction": "desc",
                        "start_from": last_point
                    }

                # Check if there are more results with a limit of 1
                additional_params["limit"] = 1
                more_results, _ = qarant.client_().scroll(**additional_params)
                has_more = len(more_results) > 0

        return {
            "next_offset": last_point if has_more else None,
            "response": processed_results,
            "has_more": has_more
        }
    except HTTPException as e:
        raise e

    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))


from fastapi.responses import JSONResponse
from fastapi import HTTPException

@search_router.get("/get_toc")
async def get_tocc(current_user: UserTenantDB = Depends(get_tenant_info)):
    try:
        import hashlib
        from typing import List, Dict

        # Step 1: Load Qdrant configuration
        qdrant_config, _ = await fetch_qdrant_config(current_user)
        if not qdrant_config:
            raise HTTPException(
                status_code=404, detail="Qdrant configuration not found"
            )

        # Step 2: Initialize Qdrant client
        qarant, _ = await initialize_clients(qdrant_config, {})

        # Step 3: Get count and fetch all documents
        count = qarant.client_().count(collection_name=qdrant_config["page_collection"])
        if count.count == 0:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No documents found")

        all_documents, _ = qarant.client_().scroll(
            qdrant_config["page_collection"], limit=count.count,
            order_by={
                "key": "created_at",
                "direction": "asc"
            }
        )

        # Step 4: Process documents and build TOC
        TOC: List[Dict[str, str]] = []
        section_titles_set = set()
        titles_set = set()

        def generate_hash_id(text: str) -> str:
            return hashlib.sha256(text.encode("utf-8")).hexdigest()

        for document in all_documents:
            payload = document.payload
            section_title = payload.get("section_title")
            title = payload.get("title")

            if title and section_title:
                section_title_id = generate_hash_id(section_title)
                title_id = generate_hash_id(title)

                if section_title not in section_titles_set:
                    section_titles_set.add(section_title)
                    TOC.append(
                        {
                            "section_name": section_title,
                            "id": section_title_id,
                            "doc_info": [],
                        }
                    )

                for section in TOC:
                    if section["id"] == section_title_id:
                        if title not in titles_set:
                            titles_set.add(title)
                            section["doc_info"].append({"name": title, "id": title_id})

        if not TOC:
            raise HTTPException(status_code=404, detail="Table of contents not found")

        return JSONResponse(content=TOC)
    except HTTPException:
        raise

    except Exception as e:
        import traceback

        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))
