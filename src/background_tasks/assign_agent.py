from src.models.user import UserTenantDB
from src.helper.logger import setup_new_logging
from src.core.activity_utils import get_active_users
from src.v2.dashboard.cta.models import CTAStatus
import random
logger = setup_new_logging(__name__)

async def assign_to_cta(current_user: UserTenantDB):
    """
    Assigns a CTA to the agent with the least open CTAs for equal workload distribution.
    Args:
        current_user: The current user with database access
    Returns:
        dict: Dictionary containing the agent ID and name
    """
    try:
        # Fetch active agents who were active in the last 24 hours
        active_agents = await get_active_users(current_user.async_db, hours=24)

        # If no active agents, randomly select from all agents in the system
        if not active_agents:
            logger.warning("No active agents found. Selecting a random agent from all agents.")
            all_agents = await current_user.async_db.users.find(
                {"role": "agent"}
            ).to_list(length=100)

            if not all_agents:
                logger.critical("No agents found in the system. Please add agents to the system.For now Returning None")
                # raise Exception("No agents found in the system")
                return {"id": None, "name": None} 

            selected_agent = random.choice(all_agents)
            selected_id = str(selected_agent["_id"])
            agent_name = selected_agent.get("username", "Unknown")
            logger.info(f"Randomly selected agent ID from all agents: {selected_id}, name: {agent_name}")
            return {"id": selected_id, "name": agent_name}

        logger.info(f"Found {len(active_agents)} active agents")

        # Get all open CTAs to analyze current workload
        open_ctas = await current_user.async_db.cta.find(
            {"status": CTAStatus.OPEN.value}
        ).to_list(length=1000)

        # Count open CTAs for each agent
        agent_workloads = {str(agent["_id"]): 0 for agent in active_agents}

        for cta in open_ctas:
            assigned_to = cta.get("assigned_to")
            if assigned_to in agent_workloads:
                agent_workloads[assigned_to] += 1

        # Find the agent(s) with the least open CTAs
        if agent_workloads:
            min_workload = min(agent_workloads.values())
            candidates = [agent_id for agent_id, count in agent_workloads.items() if count == min_workload]

            # Get all agents that have the minimum workload to ensure even distribution
            if candidates:
                selected_id = random.choice(candidates)
                # Get the agent name from the database

                from bson import ObjectId

                # Ensure agen_id is an ObjectId
                if isinstance(selected_id, str):
                    agen_id = ObjectId(selected_id)
                elif isinstance(selected_id, ObjectId):
                    agen_id = selected_id
                else:
                    raise ValueError("selected_id must be a string or ObjectId")
                logger.info(f"Selected agent ID: {agen_id}")
                selected_agent = await current_user.async_db.users.find_one(
                    {"_id": agen_id}
                )
                logger.info(f"Selected {selected_agent}")
                agent_name = selected_agent.get("username", "Unknown") if selected_agent else "Unknown"
                logger.info(f"Selected agent ID: {selected_id} with {min_workload} open CTAs, name: {agent_name}")
                return {"id": selected_id, "name": agent_name}

        # If we couldn't find an agent with minimum workload, randomly select from all active agents
        selected_agent = random.choice(active_agents)
        selected_id = str(selected_agent["_id"])
        agent_name = selected_agent.get("username", "Unknown")
        logger.info(f"Randomly selected agent ID: {selected_id}, name: {agent_name}")
        return {"id": selected_id, "name": agent_name}

    except Exception as e:
        logger.error(f"Error assigning agent to CTA: {str(e)}")
        # Randomly select from all agents in the system as fallback
        try:
            all_agents = await current_user.async_db.users.find(
                {"role": "agent"}
            ).to_list(length=100)

            if not all_agents:
                raise Exception("No agents found in the system")

            selected_agent = random.choice(all_agents)
            selected_id = str(selected_agent["_id"])
            agent_name = selected_agent.get("username", "Unknown")
            logger.warning(f"Error occurred. Randomly selected agent ID: {selected_id}, name: {agent_name}")
            return {"id": selected_id, "name": agent_name}
        except Exception as inner_e:
            logger.critical(f"Critical error in fallback assignment: {str(inner_e)}")
            raise