"""
Call Response

Input (Last Sumamry + Latest Question)

Calculate: Query+Answer(done), Product_Identify(static), Ongoing_or_first_convo, <PERSON><PERSON>(done), <PERSON>(done), Personality(done)

Calculate: Refine_Reply
"""

from pydantic import BaseModel
from typing import Optional, List, Dict, Literal
from langchain_core.runnables import RunnableMap
from src.core.database import get_admin_db
from src.models.summary import SummaryModel
from src.helper.logger import setup_new_logging
from src.v2.dashboard.cta.models import CTAType
import streamlit as st

from llama_index.core.memory import ChatSummaryMemoryBuffer
from llama_index.core.llms import ChatMessage
# from src.helper.resolve_llm import resolve_llm

from src.streamlitdemo.chatdemo.gen_query_n_reply import generate_answer_from_query, generate_query
from src.streamlitdemo.chatdemo.senti_lang import  detect_language #, sentiment_classification
# from src.streamlitdemo.chatdemo.personaliy import generate_personality
from src.streamlitdemo.chatdemo.identify_product import identify_product
# from src.v2.chat.contextualize import contextualize_messages
# from src.streamlitdemo.chatdemo.reply import refine_reply
# from src.streamlitdemo.chatdemo.phrases_to_avoid import phrases_to_avoid
# from src.streamlitdemo.chatdemo.spin_states import gather_information_for_cur_state, all_requirements_fullfilled
from src.streamlitdemo.chatdemo.simulate_tools import (
    # get_available_booking_dates,
    # book_date,
    # get_current_date,
    # check_booking_status,
    create_issue_tickets,
    # get_ticket_status,
    initial_address_information,
    handle_booking,
)

# from src.streamlitdemo.chatdemo.get_db import db
from src.helper.resolve_llm import resolve_llm
# from src.streamlitdemo.chatdemo.track_time import time_it
import openai
import json  # Add this import at the top
import asyncio

logger = setup_new_logging(__name__)

class MsgRequest(BaseModel):
    customer_id: Optional[str] = None
    chat_history_format: Literal["role_content", "role_data"] = "role_content"
    chat_history: Optional[List] = None
    message: Optional[str] = None
    message_media_values: Optional[str] = None
    image_process_metadata: Optional[Dict] = None

    current_spin_state: Optional[str] = None
    next_spin_state: Optional[str] = None
    info_gathering: Optional[dict] = None

    previous_summary: Optional[str | list] = None
    spin_states: Optional[Dict] = None
    channel: Optional[str] = None


    #let's just use message and user_id from this

    @property
    def json_data_with_fields_str_formatted(self) -> str:
        gathered_info = self.info_gathering
        gathered_info.pop("name")
        return str(gathered_info)
    def set_new_summary(self, summary_str):
        self.previous_summary = summary_str

# @time_it
# def generate_response(req: MsgRequest) -> str:
#     response_chain = RunnableMap({
#         "query_answer":lambda x: generate_answer_from_query(query=generate_query(x)),
#         # "identifed_product": identify_product,
#         # "sentiment": sentiment_classification,
#         "language": detect_language,
#         # "personality": generate_personality,

#         # "contextualize": contextualize_messages,

#         # to carry over
#         "summary_str": lambda x: x["summary_str"],
#         "latest_message_str": lambda x: x["latest_message_str"],

#         "current_spin_state": lambda x: x["current_spin_state"],
#         "next_spin_state": lambda x: x["next_spin_state"],
#         "json_data_with_fields_str_formatted": lambda x: x["json_data_with_fields_str_formatted"],
#         "spin_states": lambda x: x["spin_states"]
#     }) | {
#         "refined_reply": refine_reply,
#         "prev_values": lambda x: x
#     }


#     response = response_chain.invoke({
#         "latest_message_str": req.message,
#         "summary_str": req.previous_summary,
#         "current_spin_state": req.current_spin_state,
#         "next_spin_state": req.next_spin_state,
#         "json_data_with_fields_str_formatted": req.json_data_with_fields_str_formatted,
#         "spin_states": req.spin_states
#     })


#     # print(response)
#     # run this is background in a seperate thread

#     calculate_summary("This is a New Conversation", req.message, response.get("refined_reply"))

#     # also save the contextualize as cache

#     return response.get("refined_reply")


# @time_it
# def calculate_summary(prev_summary, user_message,ai_response) -> str:

#     prompt=db["prompt"].find_one({"name":"summary_prompt"})["text"]

#     chat_history = [
#         ChatMessage(role="system", content="Summary of the Conversation: "+prev_summary),
#         ChatMessage(role="user", content=user_message),
#         ChatMessage(role="assistant", content=ai_response)
#     ]

#     memory = ChatSummaryMemoryBuffer.from_defaults(
#         chat_history=chat_history,
#         llm=resolve_llm(model_name="models/gemini-2.0-flash"),
#         token_limit=1,  # Adjusted for meaningful summaries
#         summarize_prompt=prompt,
#         # tokenizer_fn=tokenizer_fn,
#     )

#     # Log successful processing
#     summary_ = memory.get()
#     # print(summary_)
#     return summary_[0].content


# @time_it
async def generate_response_openai(req: MsgRequest, SYS_PROMPT, qd_client, current_user) -> str:
    print("chatdemo response openai")
    SYSTEM_PROMPT = SYS_PROMPT["text"]

    source_nodes = []
    tool_calls = None
    tool_calls_results = []

    DETECT_LANGUAGE_PROMPT = current_user.db.prompt.find_one({"name":"language_detection"})
 
    IDENTIFY_PRODUCT_PROMPT = current_user.db.prompt.find_one({"name":"identify_product"})


    # Run language detection and product identification concurrently for better performance
    identify_task = asyncio.create_task(
        asyncio.to_thread(identify_product, {"latest_message_str": req.message, "prompt": IDENTIFY_PRODUCT_PROMPT}, current_user=current_user)
    )
    language_task = asyncio.create_task(
        asyncio.to_thread(detect_language, {"latest_message_str": req.message, "prompt": DETECT_LANGUAGE_PROMPT}, current_user=current_user)
    )

    identified_product, prod_usage = await identify_task
    language_response = await language_task
    # print("\n\n language_response",language_response)

       # Extract language from new format
    if isinstance(language_response, dict) and "result" in language_response:
        language = language_response["result"].get("detected_language", None)
        lang_usage = language_response.get("usage", {})
    else:
        language, lang_usage = None, {}

    org_detail=current_user.db.business_info.find_one({"name":"BusinessInfo"})
    if not org_detail:
        raise Exception("Business Detail not found. Complete the Setup ")
    

    prefered_language = org_detail.get("language", [])
    if not isinstance(prefered_language, list):
        prefered_language = [prefered_language]

    # Decision logic
    if "Auto Detect" in prefered_language and language:
        language_to_reply = language
    elif language in prefered_language:
        language_to_reply = language
    elif prefered_language:
        language_to_reply = prefered_language[0]
    else:
        language_to_reply = "english"

    print("language_to_replyyy",language_to_reply)

    ticket_required_info = org_detail.get("ticket_required_info", [])

    # Filter out 'description' and 'issue_type'
    filtered_info = [field for field in ticket_required_info if field not in ["_description", "_issue_type","description","issue_type"]]
    ticket_required_prompt=""
    booking_required_prompt=""
    greeting_prompt=""
    user_requirements=get_admin_db().prompt.find_one({"name":"user_requirement_prompt_setup"})

    if filtered_info:
        prompt_template = user_requirements.get("ticket_required_prompt", "")
        fields = ", ".join([f"{field}" for field in filtered_info])
        ticket_required_prompt = prompt_template.format(fields=fields)
        # ticket_required_prompt = prompt_template.format(fields=", ".join(filtered_info))

    AI_greeting = org_detail.get("AI_greeting", False)
    if AI_greeting:
        greeting_prompt= user_requirements.get("greeting_prompt","")
    booking = org_detail.get("booking", None)
    booking_required_info = org_detail.get("booking_required_info", [])
    if booking =="handle_booking":
        prompt_template = user_requirements.get("booking_required_prompt", "")
        filtered_booking_req = [field for field in booking_required_info if field not in ["query","description"]]
        fields = ", ".join([f"{field}" for field in filtered_booking_req])
        booking_required_prompt = prompt_template.format(fields=fields)


    # Format system prompt
    SYSTEM_PROMPT = SYSTEM_PROMPT.format(
        org_name=org_detail.get("org_name"),
        org_goal=org_detail.get("org_goal"),
        business_type=org_detail.get("org_type"),
        agent_name=org_detail.get("agent_name"),
        agent_role=org_detail.get("agent_goal"),
        org_description=org_detail.get("org_description"),
        language_to_reply=language_to_reply,
        ticket_required_prompt=ticket_required_prompt,
        booking_required_prompt=booking_required_prompt,
        greeting_prompt=greeting_prompt,
        


        # answer=data["query_answer"].get("reply"),
        identified_product=identified_product,
        language=language,
        # additional_context=data.get("additional_context", ""),
        response_mode_str="Provide a detailed and explained answer in a couple of sentences."


    )

    env = current_user.db.settings.find_one({"name": "env"})
    API_KEYS = env.get("config")
    openai_client = openai.OpenAI(api_key=API_KEYS.get("OPENAI_API_KEY"))

    print(SYSTEM_PROMPT)

    # Initialize messages list
    messages = [{"role": "system", "content": SYSTEM_PROMPT}]
    if isinstance(req.previous_summary, list):
        messages.extend(req.previous_summary)
    messages.append({"role": "user", "content": req.message})

    if req.message_media_values:
        logger.debug(f"Message Media Values: {req.message_media_values}")
        messages.append({"role": "user", "content": f"Description of User Provided Images:\n{req.message_media_values}"})

    # Define tools
    tools_ =current_user.db["tools"].find({"is_enabled": True})
    tools = []
    for tool in tools_:
        tools.append(tool["tools"][0])
    if not tools:
        raise Exception("No tools found")


    # Initial API call
    response = openai_client.chat.completions.create(
        model=SYS_PROMPT["model"],
        messages=messages,
        tools=tools,
        tool_choice="auto",
        parallel_tool_calls=True,
    )

    print("almost complete")
    original_usage ={"model":SYS_PROMPT["model"], **response.usage.model_dump()}

 
    print("almost complete2")

    # Allow multiple rounds of tool calls
    max_tool_calls = 5  # Prevent infinite loops
    current_calls = 0
    token_ussage={}
    while current_calls < max_tool_calls:
        current_calls += 1
        assistant_message = response.choices[0].message
        messages.append(assistant_message)

        if not assistant_message.tool_calls:
            break

        tool_calls = assistant_message.tool_calls

        # Handle all tool calls in this round
        for tool_call in assistant_message.tool_calls:
            function_name = tool_call.function.name
            function_args = json.loads(tool_call.function.arguments)
            print("function_args",function_args)
            tool_call_id = tool_call.id  # Store the tool call ID

            # Execute the appropriate function
            if function_name == "search_database":
                # print(f"Round {current_calls} - FUNCTION ARGS for search: {function_args}")
                result, source_nodes = await asyncio.to_thread(
                    generate_answer_from_query,
                    query=function_args["query"],
                    qd_client=qd_client,
                    current_user=current_user
                )
                source_nodes = source_nodes

            elif function_name == "initial_address_information":
                # print(f"Round {current_calls} - FUNCTION ARGS for initial: {function_args}")
                result = await asyncio.to_thread(initial_address_information, db=current_user.db)

            elif function_name == "create_issue_tickets":
                print(f"Round {current_calls} - FUNCTION ARGS for create: {function_args}")
                
                # Get the tool definition to check required fields
                ticket_tool = None
                for tool in tools:
                    if tool.get("type") == "function" and tool.get("function", {}).get("name") == "create_issue_tickets":
                        ticket_tool = tool
                        break

                # Extract required fields from the tool definition
                required_fields = []
                if ticket_tool:
                    required_fields = ticket_tool.get("function", {}).get("parameters", {}).get("required", [])
                    print(f"Required fields from tool definition: {required_fields}")
                else:
                    print("WARNING: ticket_tool not found in tools definition!")

                # Define placeholder values that should be treated as missing
                placeholder_values = {
                    "not provided", "n/a", "none", "null", "undefined", "",
                    "not available", "na", "nil", "empty", "-", "0",
                    "missing", "unknown", "tbd", "to be determined"
                }

                def is_field_valid(value, field_name=None):
                    """Check if a value is valid (not empty or placeholder)"""
                    if value is None:
                        return False
                    if isinstance(value, str):
                        value = value.lower().strip()
                        if value in placeholder_values or not bool(value):
                            return False
                    return True

                # Validate required fields
                missing_fields = [field for field in required_fields if not is_field_valid(function_args.get(field), field)]
                
                if missing_fields:
                    print(f"VALIDATION FAILED - Missing required fields for ticket: {missing_fields}")
                    result = {
                        "error": f"Missing required fields for ticket: {', '.join(missing_fields)}",
                        "missing_fields": missing_fields
                    }
                else:
                    # Set default issue_type if not provided
                    if not function_args.get("issue_type"):
                        print("Issue type not provided, defaulting to ticket")
                        function_args["issue_type"] = CTAType.TICKET.value

                    # Prepare extra fields (all non-standard fields)
                    extra_fields = {k: v for k, v in function_args.items() 
                                if k not in ["name", "issue_type", "description", "customer_phone_no"]}
                    
                    try:
                        print("VALIDATION PASSED - Creating ticket")
                        result = await asyncio.to_thread(
                            create_issue_tickets,
                            name=function_args["name"],
                            issue_type=function_args["issue_type"],
                            description=function_args["description"],
                            user_req=req,
                            current_user=current_user,
                            channel=req.channel,
                            **extra_fields
                        )
                        function_args["cta_id"] = result["cta_id"]
                    except Exception as e:
                        print(f"Error in create_issue_tickets: {e}")
                        result = {"error": f"Error in create_issue_tickets: {str(e)}"}

            # elif function_name == "handle_booking":
            #     # Get the tool definition to check required fields
            #     booking_tool = None
            #     for tool in tools:
            #         if tool.get("type") == "function" and tool.get("function", {}).get("name") == "handle_booking":
            #             booking_tool = tool
            #             break
                
            #     # Extract required fields from the tool definition
            #     required_fields = []
            #     if booking_tool:
            #         required_fields = booking_tool.get("function", {}).get("parameters", {}).get("required", [])
            #         print(f"Required fields from tool definition: {required_fields}")
            #     else:
            #         print("WARNING: booking_tool not found in tools definition!")

            #     # Debug: Log original function_args
            #     print(f"Original function_args: {function_args}")

            #     # Get only non-empty values from function_args
            #     booking_args = {}

            #     # Define placeholder values that should be treated as missing
            #     placeholder_values = {
            #         "not provided", "n/a", "none", "null", "undefined", "",
            #         "not available", "na", "nil", "empty", "-", "0",
            #         "missing", "unknown", "tbd", "to be determined"
            #     }

            #     def is_tool_valid(value, field_name=None):
            #         """Check if a value is valid (not empty or placeholder)"""
            #         # Exception fields that are always considered valid (optional fields)
            #         optional_fields = {"customer_medical_history", "intrested_topic", "customer_age"}

            #         # If it's an optional field, always return True (allow any value including empty/placeholder)
            #         if field_name and field_name in optional_fields:
            #             return True

            #         if value is None:
            #             return False
            #         if isinstance(value, str):
            #             value = value.lower().strip()
            #             # Check if it's in placeholder values or effectively empty
            #             if value in placeholder_values or not bool(value):
            #                 return False
            #         return True

            #     # Only add valid values to booking_args
            #     if is_tool_valid(function_args.get("customer_name"), "customer_name"):
            #         booking_args["customer_name"] = function_args.get("customer_name")

            #     if is_tool_valid(function_args.get("customer_phone_no"), "customer_phone_no"):
            #         booking_args["customer_phone_no"] = function_args.get("customer_phone_no")

            #     if is_tool_valid(function_args.get("customer_age"), "customer_age"):
            #         booking_args["customer_age"] = function_args.get("customer_age")

            #     if is_tool_valid(function_args.get("customer_medical_history"), "customer_medical_history"):
            #         booking_args["customer_medical_history"] = function_args.get("customer_medical_history")

            #     if is_tool_valid(function_args.get("description"), "description"):
            #         booking_args["description"] = function_args.get("description")

            #     print("booking_args:", booking_args)

            #     # Simplified and strict validation logic
            #     if required_fields:
            #         # Check each required field using the strict is_tool_valid function
            #         missing_fields = [field for field in required_fields if not is_tool_valid(function_args.get(field), field)]
            #         print(f"DEBUG - Checking required fields: {required_fields}")
            #         for field in required_fields:
            #             field_value = function_args.get(field)
            #             is_valid = is_tool_valid(field_value, field)
            #             print(f"DEBUG - Field '{field}': value='{field_value}', is_valid={is_valid}")

            #         if missing_fields:
            #             print(f"VALIDATION FAILED - Missing required fields for booking: {missing_fields}")
            #             print(f"BOOKING WILL NOT BE CREATED - Returning error instead")
            #             result = {
            #                 "error": f"Missing required fields for booking: {', '.join(missing_fields)}",
            #                 "missing_fields": missing_fields
            #             }
            #             # DO NOT call handle_booking when validation fails
            #         else:
            #             # All required fields are present, proceed with booking
            #             try:
            #                 print("VALIDATION PASSED - All required fields present, creating booking")
            #                 result, data = await asyncio.to_thread(
            #                     handle_booking,
            #                     **booking_args,
            #                     user_req=req,
            #                     current_user=current_user
            #                 )
            #                 function_args["cta_id"] = data["cta_id"]
            #                 function_args["issue_type"] = CTAType.BOOKING.value
            #             except Exception as e:
            #                 print(f"Error in handle_booking: {e}")
            #                 result = {"error": f"Error in handle_booking: {str(e)}"}
            #     else:
            #         # No required fields defined in tool definition
            #         # Check if we have at least basic required info (name and phone)
            #         missing_basic = []
            #         if not is_tool_valid(function_args.get("customer_name"), "customer_name"):
            #             missing_basic.append("customer_name")
            #         if not is_tool_valid(function_args.get("customer_phone_no"), "customer_phone_no"):
            #             missing_basic.append("customer_phone_no")

            #         print(f"DEBUG - No required fields defined, checking basic fields")
            #         print(f"DEBUG - customer_name: '{function_args.get('customer_name')}', valid: {is_tool_valid(function_args.get('customer_name'), 'customer_name')}")
            #         print(f"DEBUG - customer_phone_no: '{function_args.get('customer_phone_no')}', valid: {is_tool_valid(function_args.get('customer_phone_no'), 'customer_phone_no')}")

            #         if missing_basic:
            #             print("VALIDATION FAILED - Missing basic required info (name or phone)")
            #             print("BOOKING WILL NOT BE CREATED - Returning error instead")
            #             result = {
            #                 "required_missing": f"Missing basic required information: {', '.join(missing_basic)}",
            #                 "missing_fields": missing_basic
            #             }
            #         else:
            #             # Basic info present, proceed with booking
            #             try:
            #                 print("VALIDATION PASSED - Basic info present, creating booking")
            #                 result, data = await asyncio.to_thread(
            #                     handle_booking,
            #                     **booking_args,
            #                     user_req=req,
            #                     current_user=current_user
            #                 )
            #                 function_args["cta_id"] = data["cta_id"]
            #                 function_args["issue_type"] = CTAType.BOOKING.value
            #             except Exception as e:
            #                 print(f"Error in handle_booking: {e}")
            #                 result = {"error": f"Error in handle_booking: {str(e)}"}
            elif function_name == "handle_booking":
                print(f"Round {current_calls} - FUNCTION ARGS for booking: {function_args}")
                
                # Get the tool definition to check required fields
                booking_tool = None
                for tool in tools:
                    if tool.get("type") == "function" and tool.get("function", {}).get("name") == "handle_booking":
                        booking_tool = tool
                        break

                # Extract required fields from the tool definition
                required_fields = []
                if booking_tool:
                    required_fields = booking_tool.get("function", {}).get("parameters", {}).get("required", [])
                    print(f"Required fields from tool definition: {required_fields}")
                else:
                    print("WARNING: booking_tool not found in tools definition!")

                # Define placeholder values that should be treated as missing
                placeholder_values = {
                    "not provided", "n/a", "none", "null", "undefined", "",
                    "not available", "na", "nil", "empty", "-", "0",
                    "missing", "unknown", "tbd", "to be determined"
                }

                def is_field_valid(value, field_name=None):
                    """Check if a value is valid (not empty or placeholder)"""
                    # Exception fields that are always considered valid (optional fields)
                    optional_fields = {"customer_medical_history", "intrested_topic", "customer_age"}
                    
                    # If it's an optional field, always return True (allow any value including empty/placeholder)
                    if field_name and field_name in optional_fields:
                        return True
                        
                    if value is None:
                        return False
                    if isinstance(value, str):
                        value = value.lower().strip()
                        if value in placeholder_values or not bool(value):
                            return False
                    return True

                # Validate required fields
                missing_fields = [field for field in required_fields if not is_field_valid(function_args.get(field), field)]
                
                if missing_fields:
                    print(f"VALIDATION FAILED - Missing required fields for booking: {missing_fields}")
                    result = {
                        "error": f"Missing required fields for booking: {', '.join(missing_fields)}",
                        "missing_fields": missing_fields
                    }
                else:
                    # Set default issue_type if not provided
                    if not function_args.get("issue_type"):
                        print("Issue type not provided, defaulting to booking")
                        function_args["issue_type"] = CTAType.BOOKING.value

                    # Prepare booking_args with all valid fields
                    booking_args = {k: v for k, v in function_args.items() 
                                if is_field_valid(v, k) or k in required_fields}
                    
                    try:
                        print("VALIDATION PASSED - Creating booking")
                        result, data = await asyncio.to_thread(
                            handle_booking,
                            **booking_args,
                            user_req=req,
                            current_user=current_user
                        )
                        function_args["cta_id"] = data["cta_id"]
                        function_args["issue_type"] = CTAType.BOOKING.value
                    except Exception as e:
                        print(f"Error in handle_booking: {e}")
                        result = {"error": f"Error in handle_booking: {str(e)}"}

            else:
                result = {"error": f"Unknown function: {function_name}"}

            # Always add the result to tool_calls_results
            tool_calls_results.append({
                "result": result,
                "function_name": function_name,
                "function_args": function_args
            })
            
            # Always add a tool message response for each tool call
            messages.append({
                "role": "tool",
                "name": function_name,
                "content": json.dumps(result),
                "tool_call_id": tool_call_id  # Use the stored tool call ID
            })

        # Get next response with tool results
        response = openai_client.chat.completions.create(
            model=SYS_PROMPT["model"],
            messages=messages,
            tools=tools,
            tool_choice="auto",  # Allow more tool calls if needed
        )
        token_ussage={"model":"gpt-4o", **response.usage.model_dump()}
        # final_response = response.choices[0].message.content
        # messages.append(response.choices[0].message)
    # Final response without tools to summarize all findings
    messages.append({"role": "user", "content": req.message})
    messages.append({"role": "assistant", "content": response.choices[0].message.content})
    # final_call = openai_client.chat.completions.create(
    #     model="gpt-4o",
    #     messages=messages,
    #     tools=tools,
    #     tool_choice="none"  # Don't allow additional tool calls
    # )
    # final_response = final_call.choices[0].message.content
    # from pprint import pprint
    # pprint(messages)


    return messages[-1]["content"], {
        "source_nodes": source_nodes,
        "tool_calls": tool_calls,
        "tool_calls_results": tool_calls_results,
        "identified_product": identified_product,
        "language": language,
        "token_usage": {
            "input_tokens": original_usage,
            "output_tokens": token_ussage,
            "lang_usage": lang_usage,
            "identify_prod_usage": prod_usage
        },
        "image_process_cost": req.image_process_metadata
    }
